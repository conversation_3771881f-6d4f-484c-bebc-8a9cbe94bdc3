import logging
import os
import threading
import asyncio
from datetime import datetime, timed<PERSON>ta
from math import ceil
from fastapi import BackgroundTasks, WebSocket
import json
from collections import defaultdict

import aiohttp
import requests
from sqlalchemy import func, and_, or_
from sqlalchemy import case
from sqlalchemy.orm import aliased

from sqlalchemy import desc
from apps.actions_v2 import ActionHandlerV2
from models import Message, Room, Consultant, Admin, User
from models.calls import Call
from schemas.models import MessageModel
from schemas.request_types_v2 import StartRoom
from schemas.response_types import RoomsResponse, HistoryResponse, RoomListResponse, CallListResponse, CallResponse
from utils.fcm_notification import send_notification, send_silent_notification
from utils.paginate import paginate
from .signature import get_signature_data
from urllib.parse import urlencode
from datetime import datetime, timezone
from utils.hlog import log_message

def get_language_name(code):
    langs = {
        'en': 'English',
        'fa': 'Persian',
        'ar': 'Arabic',
        'fr': 'French',
        'in': 'Indonesian',
        'tr': 'Turkish',
        'az': 'Azerbaijani',
        'ru': 'Russian',
        'sw': 'Swahili',
        'es': 'Spanish',
        'ur': 'Urdu',
        'de': 'German',
    }
    return langs.get(code, 'English')

class ActionHandlerV3(ActionHandlerV2):
    def __init__(self):
        super().__init__()
        self.actions['getHistory'] = self.get_history
        self.actions['vmRequest'] = self.vm_request
        self.actions['setFCM'] = self.set_fcm
        self.actions['streamStarted'] = self.stream_started_handler
        self.actions['streamStopped'] = self.stream_stopped_handler
        self.actions['payForNextPeriod'] = self.pay_for_next_period_handler
        self.actions['consultantResponseCall'] = self.handle_consultant_response_event
        self.actions['cancelCall'] = self.cancel_call
        self.actions['recentCalls'] = self.get_recent_calls
        self.consultant_responses = defaultdict(asyncio.Queue)
        
    def is_on_another_call(self, consultant):
        """
        Check if a consultant is currently on another call.
        
        Args:
            consultant: Consultant object to check
            
        Returns:
            bool: True if consultant is on another active call, False otherwise
        """
        try:
            # Check for active calls where this consultant is participating
            active_call = self.db.session.query(Call).filter(
                Call.consultant_id == consultant.id,
                Call.status == "confirmed",
                Call.end_time.is_(None)
            ).first()
            
            return active_call is not None
        except Exception as e:
            logging.error(f"Error checking if consultant is on another call: {e}")
            return False
        

    async def set_fcm(self, data):
        # Extract platform from WebSocket connection
        platform = getattr(self.connection, 'platform', 'android')

        if type(self.from_user) is Consultant:
            self.db.session.query(Consultant).filter(
                Consultant.username == self.from_user.username,
                ).update({
                Consultant.fcm: data['token'],
                Consultant.device_os: platform,
            })


        elif type(self.from_user) is User:
            self.db.session.query(User).filter(
                User.username == self.from_user.username,
                ).update({
                User.fcm: data['token'],
                User.device_os: platform,
            })

        self.db.session.commit()

        return {
            'action': data['action'],
            'status': True,
        }


    
    async def vm_request(self, data):
        """
            Voice / Video message Request 
        """

        consultant = self.db.session.query(Consultant).filter(Consultant.username == data['consultant']).first()
        if not consultant:
                return {'action': data['action'], 'error': 'not consultant', 'status': False}
            
        # if data['chat_type'] == 'video':
        #     if consultant.video_call_cost is None:
        #         return {'action': data['action'], 'error': 'consultant not video call ', 'status': False}
        #     cost = consultant.video_call_cost

        # elif data['chat_type'] == 'voice':
        #     if consultant.voice_call_cost is None:
        #         return {'act': data['action'], 'error': 'consultant not voice call ', 'status': False}
        #     cost = consultant.voice_call_cost

        if consultant.status == "busy" or consultant.status == "offline":
            return {'act': data['action'], 'error': 'consultant offline or busy', 'status': False}
            
            
        # has_balance = await credit_balance(self.from_user, cost)
        # if not has_balance:
            # return {'act': data['action'], 'error': 'Insufficient balance', 'status': False}

        
        call = Call(
            consultant_id=consultant.id,
            client_id=self.from_user.id,
            start_time=datetime.now(),
            call_type=data['chat_type'],
            cost=consultant.video_call_cost if data['chat_type'] == 'video' else consultant.voice_call_cost,
            status='unconfirmed'
        )
        self.db.session.add(call)
        self.db.session.commit()
        self.db.session.refresh(call)             
        
        if not call.consultant and not call.consultant.fcm:            
            await self.connection.send_text(json.dumps({'act': 'callFailed', 'status': False, 'error': 'Failed to send notification to consultant.'}))
            
        await self.connection.send_text(json.dumps({'act': data['action'], 'call_id': call.id, 'status': True, 'message': 'call prossing'}))
