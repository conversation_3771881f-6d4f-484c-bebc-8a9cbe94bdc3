from starlette.authentication import (
    AuthenticationBackend,
    AuthCredentials
)

from models import Ad<PERSON>, Consultant, User
from utils.save_models import get_or_save_user, fetch_user
from utils.type_hints import WebSocket
from fastapi_sqlalchemy import db
from sqlalchemy import func, or_


class GuestUser:
    is_authenticated = True
    user_type = 'guest'
    username = 'guest'
    language_code = 'en'

    def to_dict(self):
        return {
            "username": self.username,
            "fullname": 'guest',
            "country": '',
            "city": '',
            "avatar_url": '',
            "is_consultant": False,
            "is_banned": False,
            "language_code": self.language_code,
        }


class BasicAuthBackend(AuthenticationBackend):
    async def authenticate(self, websocket: WebSocket, *args, **kwargs):
        lang_code = websocket.query_params.get('language_code', None)
        auth_token = websocket.query_params.get('t', '')
        ws_version = int(websocket.query_params.get('version') or 1)

        if auth_token == 'guest':
            guest_user = GuestUser()
            guest_user.language_code = lang_code
            return AuthCredentials(['authenticated']), guest_user

        if ws_version == 2 or ws_version == 3:
            habib_user = fetch_user(auth_token)
            try:
                habib_client = habib_user.get('username').split(':')
                habib_user_email = habib_client[0]
                habib_user_id = habib_client[1] if len(habib_client) > 1 else None 
            except Exception as e:
                habib_user_email = 'noemail'
                habib_user_id = None
            with db():
                try:
                    consultant = None
                    if habib_user_id:
                       consultant = db.session.query(Consultant).filter(
                           Consultant.user_id == habib_user_id
                       ).first()
                    if not consultant:
                        consultant = db.session.query(Consultant).filter(
                            or_(
                                Consultant.token == auth_token,  
                                Consultant.token.like(f'%,{auth_token}'),  
                                Consultant.token.like(f'{auth_token},%'),  
                                Consultant.token.like(f'%,{auth_token},%')  
                            )
                        ).first()
                    if not consultant:  
                        consultant = db.session.query(Consultant).filter(
                            Consultant.username == habib_user_email
                        ).first()
                    
                    consultant.is_authenticated = True
                    consultant.user_type = 'Consultant'
                    if not consultant.is_banned:
                        return AuthCredentials(['authenticated']), consultant
                except Exception as e:
                    pass

        if auth_token.startswith('consultant:'):

            token_part = auth_token.replace('consultant:', '').split(',')[0][:40]
            with db():
                # جستجوی مشاور با استفاده از شرط‌های like مشابه ورژن 2 و 3
                consultant = db.session.query(Consultant).filter(
                    or_(
                        Consultant.token == token_part,
                        Consultant.token.like(f'%,{token_part}'),
                        Consultant.token.like(f'{token_part},%'),
                        Consultant.token.like(f'%,{token_part},%')
                    )
                ).first()
                if consultant:
                    consultant.is_authenticated = True
                    consultant.user_type = 'Consultant'
                    return AuthCredentials(['authenticated']), consultant

        elif auth_token.startswith('admin:'):
            auth_token = auth_token.replace('admin:', '')
            auth_token = auth_token[:40]
            with db():
                if admin := db.session.query(Admin).filter(Admin.token == auth_token).first():
                    admin.is_authenticated = True
                    admin.user_type = 'Admin'
                    return AuthCredentials(['authenticated']), admin

        else:
            with db():
                if user := get_or_save_user(auth_token, db):
                    print(f'--user=--> {user}')
                    user.is_authenticated = True
                    user.user_type = 'User'
                    websocket.state.user_id = user.id
                    return AuthCredentials(["authenticated"]), user

        print(auth_token, 'not authenticated')

    print("no token")
